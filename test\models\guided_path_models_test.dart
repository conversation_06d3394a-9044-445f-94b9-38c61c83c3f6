import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';

void main() {
  group('GuidedPath Model Tests', () {
    test('should create GuidedPath with required fields', () {
      final now = DateTime.now();
      final guidedPath = GuidedPath(
        name: 'Test Path',
        category: 'Focus & Productivity',
        description: 'A test guided path',
        stepCount: 5,
        targetUserTier: 'free',
        createdAt: now,
      );

      expect(guidedPath.name, 'Test Path');
      expect(guidedPath.category, 'Focus & Productivity');
      expect(guidedPath.description, 'A test guided path');
      expect(guidedPath.stepCount, 5);
      expect(guidedPath.targetUserTier, 'free');
      expect(guidedPath.isActive, true); // Default value
      expect(guidedPath.createdAt, now);
    });

    test('should serialize to and from JSON correctly', () {
      final now = DateTime.now();
      final guidedPath = GuidedPath(
        id: 'test-id',
        name: 'Test Path',
        category: 'Focus & Productivity',
        description: 'A test guided path',
        stepCount: 5,
        targetUserTier: 'free',
        imageUrl: 'https://example.com/image.jpg',
        estimatedCompletionTimeMinutes: 120,
        difficultyLevel: 'beginner',
        prerequisites: ['Basic knowledge'],
        isActive: true,
        createdAt: now,
        updatedAt: now,
        metadata: {'test': 'value'},
      );

      // Serialize to JSON
      final json = guidedPath.toJson();

      // Verify JSON structure
      expect(json['id'], 'test-id');
      expect(json['name'], 'Test Path');
      expect(json['category'], 'Focus & Productivity');
      expect(json['stepCount'], 5);
      expect(json['targetUserTier'], 'free');
      expect(json['isActive'], true);
      expect(json['prerequisites'], ['Basic knowledge']);
      expect(json['metadata'], {'test': 'value'});

      // Deserialize from JSON
      final deserializedPath = GuidedPath.fromJson(json);

      // Verify deserialized object
      expect(deserializedPath.id, guidedPath.id);
      expect(deserializedPath.name, guidedPath.name);
      expect(deserializedPath.category, guidedPath.category);
      expect(deserializedPath.stepCount, guidedPath.stepCount);
      expect(deserializedPath.targetUserTier, guidedPath.targetUserTier);
      expect(deserializedPath.isActive, guidedPath.isActive);
      expect(deserializedPath.prerequisites, guidedPath.prerequisites);
      expect(deserializedPath.metadata, guidedPath.metadata);
    });

    test('should create copy with updated fields', () {
      final now = DateTime.now();
      final original = GuidedPath(
        name: 'Original Path',
        category: 'Focus & Productivity',
        description: 'Original description',
        stepCount: 5,
        targetUserTier: 'free',
        createdAt: now,
      );

      final updated = original.copyWith(
        name: 'Updated Path',
        stepCount: 7,
        targetUserTier: 'paid',
      );

      expect(updated.name, 'Updated Path');
      expect(updated.stepCount, 7);
      expect(updated.targetUserTier, 'paid');
      // Unchanged fields should remain the same
      expect(updated.category, original.category);
      expect(updated.description, original.description);
      expect(updated.createdAt, original.createdAt);
    });
  });

  group('PathStep Model Tests', () {
    test('should create PathStep with required fields', () {
      final now = DateTime.now();
      final pathStep = PathStep(
        pathId: 'test-path-id',
        stepNumber: 1,
        title: 'Test Step',
        description: 'A test step description',
        completionCriteria: 'Complete the test',
        createdAt: now,
      );

      expect(pathStep.pathId, 'test-path-id');
      expect(pathStep.stepNumber, 1);
      expect(pathStep.title, 'Test Step');
      expect(pathStep.description, 'A test step description');
      expect(pathStep.completionCriteria, 'Complete the test');
      expect(pathStep.isActive, true); // Default value
      expect(pathStep.createdAt, now);
    });

    test('should serialize to and from JSON correctly', () {
      final now = DateTime.now();
      final pathStep = PathStep(
        id: 'step-id',
        pathId: 'test-path-id',
        stepNumber: 2,
        title: 'Test Step',
        description: 'A test step description',
        completionCriteria: 'Complete the test',
        estimatedDurationMinutes: 30,
        resources: [
          ExternalResource(
            title: 'Resource 1',
            link: 'https://example.com/resource1',
            type: ExternalResourceType.article,
          ),
          ExternalResource(
            title: 'Resource 2',
            link: 'https://example.com/resource2',
            type: ExternalResourceType.video,
            durationMinutes: 10,
          ),
        ],
        reflectionPrompts: ['Prompt 1', 'Prompt 2'],
        isActive: true,
        createdAt: now,
        metadata: {'step_type': 'practice'},
      );

      // Serialize to JSON
      final json = pathStep.toJson();

      // Verify JSON structure
      expect(json['id'], 'step-id');
      expect(json['pathId'], 'test-path-id');
      expect(json['stepNumber'], 2);
      expect(json['title'], 'Test Step');
      expect(json['resources'], isA<List>());
      expect(json['resources'].length, 2);
      expect(json['resources'][0]['title'], 'Resource 1');
      expect(json['resources'][0]['type'], 'article');
      expect(json['resources'][1]['title'], 'Resource 2');
      expect(json['resources'][1]['type'], 'video');
      expect(json['reflectionPrompts'], ['Prompt 1', 'Prompt 2']);
      expect(json['metadata'], {'step_type': 'practice'});

      // Deserialize from JSON
      final deserializedStep = PathStep.fromJson(json);

      // Verify deserialized object
      expect(deserializedStep.id, pathStep.id);
      expect(deserializedStep.pathId, pathStep.pathId);
      expect(deserializedStep.stepNumber, pathStep.stepNumber);
      expect(deserializedStep.title, pathStep.title);
      expect(deserializedStep.resources, pathStep.resources);
      expect(deserializedStep.reflectionPrompts, pathStep.reflectionPrompts);
      expect(deserializedStep.metadata, pathStep.metadata);
    });
  });

  group('UserPathProgress Model Tests', () {
    test('should create UserPathProgress with required fields', () {
      final now = DateTime.now();
      final progress = UserPathProgress(
        userId: 'user-123',
        pathId: 'path-456',
        isCompleted: false,
        startedDate: now,
        lastAccessedDate: now,
      );

      expect(progress.userId, 'user-123');
      expect(progress.pathId, 'path-456');
      expect(progress.isCompleted, false); // Default value
      expect(progress.startedDate, now);
      expect(progress.lastAccessedDate, now);
    });

    test('should serialize to and from JSON correctly', () {
      final now = DateTime.now();
      final stepProgress = {
        'step1': PathStepProgress(
          stepId: 'step1',
          isCompleted: true,
          completedDate: now,
        ),
        'step2': PathStepProgress(
          stepId: 'step2',
          isCompleted: false,
          startedDate: now,
        ),
      };

      final progress = UserPathProgress(
        id: 'progress-id',
        userId: 'user-123',
        pathId: 'path-456',
        isCompleted: false,
        startedDate: now,
        lastAccessedDate: now,
        completionDate: now,
        stepProgress: stepProgress,
      );

      // Serialize to JSON
      final json = progress.toJson();

      // Verify JSON structure
      expect(json['id'], 'progress-id');
      expect(json['userId'], 'user-123');
      expect(json['pathId'], 'path-456');
      expect(json['isCompleted'], false);
      expect(json['stepProgress'], isA<Map<String, dynamic>>());
      expect(json['stepProgress']['step1']['isCompleted'], true);
      expect(json['stepProgress']['step2']['isCompleted'], false);

      // Deserialize from JSON
      final deserializedProgress = UserPathProgress.fromJson(json);

      // Verify deserialized object
      expect(deserializedProgress.id, progress.id);
      expect(deserializedProgress.userId, progress.userId);
      expect(deserializedProgress.pathId, progress.pathId);
      expect(deserializedProgress.isCompleted, progress.isCompleted);
      expect(
        deserializedProgress.stepProgress?.length,
        progress.stepProgress?.length,
      );
      expect(deserializedProgress.stepProgress?['step1']?.isCompleted, true);
      expect(deserializedProgress.stepProgress?['step2']?.isCompleted, false);
    });

    test('should provide helper methods for progress tracking', () {
      final now = DateTime.now();
      final stepProgress = {
        'step1': PathStepProgress(
          stepId: 'step1',
          isCompleted: true,
          completedDate: now,
        ),
        'step2': PathStepProgress(
          stepId: 'step2',
          isCompleted: true,
          completedDate: now,
        ),
        'step3': PathStepProgress(
          stepId: 'step3',
          isCompleted: false,
          startedDate: now,
        ),
      };

      final progress = UserPathProgress(
        userId: 'user-123',
        pathId: 'path-456',
        isCompleted: false,
        startedDate: now,
        lastAccessedDate: now,
        stepProgress: stepProgress,
      );

      // Test completion status
      expect(progress.isCompleted, false);

      // Test progress percentage
      expect(
        progress.getProgressPercentage(6),
        closeTo(0.33, 0.01),
      ); // 2 completed out of 6 total
      expect(progress.getProgressPercentage(0), 0.0); // Edge case

      // Test completed steps count
      expect(progress.getCompletedStepsCount(), 2);
    });

    test('should handle completed status correctly', () {
      final now = DateTime.now();
      final stepProgress = {
        'step1': PathStepProgress(stepId: 'step1', isCompleted: true),
        'step2': PathStepProgress(stepId: 'step2', isCompleted: true),
        'step3': PathStepProgress(stepId: 'step3', isCompleted: true),
        'step4': PathStepProgress(stepId: 'step4', isCompleted: true),
        'step5': PathStepProgress(stepId: 'step5', isCompleted: true),
        'step6': PathStepProgress(stepId: 'step6', isCompleted: true),
      };

      final completedProgress = UserPathProgress(
        userId: 'user-123',
        pathId: 'path-456',
        isCompleted: true,
        startedDate: now,
        lastAccessedDate: now,
        completionDate: now,
        stepProgress: stepProgress,
      );

      expect(completedProgress.isCompleted, true);
      expect(completedProgress.getProgressPercentage(6), 1.0);
      expect(completedProgress.getCompletedStepsCount(), 6);
    });

    test('should handle step progress tracking with PathStep IDs', () {
      final now = DateTime.now();
      final stepId1 = 'pathstep_abc123def456';
      final stepId2 = 'pathstep_xyz789ghi012';

      final progress = UserPathProgress(
        userId: 'user-123',
        pathId: 'path-456',
        isCompleted: false,
        startedDate: now,
        lastAccessedDate: now,
      );

      // Test initial state
      expect(progress.getStepProgress(stepId1), isNull);
      expect(progress.getStepChatId(stepId1), isNull);
      expect(progress.hasStepChatId(stepId1), false);
      expect(progress.getStepStatus(stepId1), false);

      // Test setting chat ID
      final progressWithChat = progress.setStepChatId(stepId1, 'chat_123');
      expect(progressWithChat.getStepChatId(stepId1), 'chat_123');
      expect(progressWithChat.hasStepChatId(stepId1), true);
      expect(progressWithChat.getStepStatus(stepId1), false);

      // Test marking step as completed
      final progressCompleted = progressWithChat.markStepCompleted(stepId1);
      expect(progressCompleted.getStepStatus(stepId1), true);
      expect(progressCompleted.getCompletedStepsCount(), 1);
      expect(
        progressCompleted.getStepChatId(stepId1),
        'chat_123',
      ); // Should preserve chat ID

      // Test multiple steps
      final progressMultiple = progressCompleted.setStepChatId(
        stepId2,
        'chat_456',
      );
      expect(progressMultiple.getStepChatId(stepId1), 'chat_123');
      expect(progressMultiple.getStepChatId(stepId2), 'chat_456');
      expect(progressMultiple.getStepStatus(stepId1), true);
      expect(progressMultiple.getStepStatus(stepId2), false);
    });
  });
}
