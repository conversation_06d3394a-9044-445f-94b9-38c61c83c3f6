import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';

void main() {
  group('PathStepProgress JSON Serialization', () {
    test('should handle null DateTime fields correctly', () {
      // Create a PathStepProgress with null DateTime fields
      final pathStepProgress = PathStepProgress(
        stepId: 'pathstep_abc123def456',
        chatId: null,
        isCompleted: false,
        startedDate: null,
        completedDate: null,
      );

      // Convert to JSON
      final json = pathStepProgress.toJson();

      // Verify JSON structure
      expect(json['stepId'], equals('pathstep_abc123def456'));
      expect(json['chatId'], isNull);
      expect(json['isCompleted'], equals(false));
      expect(json['startedDate'], isNull);
      expect(json['completedDate'], isNull);

      // Convert back from JSON
      final fromJson = PathStepProgress.fromJson(json);

      // Verify all fields are correctly deserialized
      expect(fromJson.stepId, equals('pathstep_abc123def456'));
      expect(fromJson.chatId, isNull);
      expect(fromJson.isCompleted, equals(false));
      expect(fromJson.startedDate, isNull);
      expect(fromJson.completedDate, isNull);
    });

    test('should handle non-null DateTime fields correctly', () {
      final now = DateTime.now();

      // Create a PathStepProgress with non-null DateTime fields
      final pathStepProgress = PathStepProgress(
        stepId: 'pathstep_xyz789ghi012',
        chatId: 'chat_123',
        isCompleted: true,
        startedDate: now,
        completedDate: now,
      );

      // Convert to JSON
      final json = pathStepProgress.toJson();

      // Convert back from JSON
      final fromJson = PathStepProgress.fromJson(json);

      // Verify all fields are correctly deserialized
      expect(fromJson.stepId, equals('pathstep_xyz789ghi012'));
      expect(fromJson.chatId, equals('chat_123'));
      expect(fromJson.isCompleted, equals(true));
      expect(fromJson.startedDate, isNotNull);
      expect(fromJson.completedDate, isNotNull);
    });

    test('should handle mixed null and non-null DateTime fields', () {
      final now = DateTime.now();

      // Create a PathStepProgress with mixed DateTime fields
      final pathStepProgress = PathStepProgress(
        stepId: 'pathstep_mno345pqr678',
        chatId: 'chat_456',
        isCompleted: false,
        startedDate: now,
        completedDate: null, // Still in progress, so no completion date
      );

      // Convert to JSON
      final json = pathStepProgress.toJson();

      // Convert back from JSON
      final fromJson = PathStepProgress.fromJson(json);

      // Verify all fields are correctly deserialized
      expect(fromJson.stepId, equals('pathstep_mno345pqr678'));
      expect(fromJson.chatId, equals('chat_456'));
      expect(fromJson.isCompleted, equals(false));
      expect(fromJson.startedDate, isNotNull);
      expect(fromJson.completedDate, isNull);
    });
  });
}
