import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart' as models;

void main() {
  group('Step Locking Logic Tests', () {
    late models.User adminUser;
    late models.User regularUser;
    late models.PathStep step1;
    late models.PathStep step2;
    late models.PathStep step3;

    setUp(() {
      final now = DateTime.now();

      adminUser = models.User(
        id: 'admin-user',
        email: '<EMAIL>',
        name: 'Admin User',
        isAdmin: true,
        createdAt: now,
      );

      regularUser = models.User(
        id: 'regular-user',
        email: '<EMAIL>',
        name: 'Regular User',
        isAdmin: false,
        createdAt: now,
      );

      step1 = models.PathStep(
        id: 'step-1',
        pathId: 'test-path',
        stepNumber: 1,
        title: 'Step 1',
        description: 'First step',
        completionCriteria: 'Complete step 1',
        createdAt: now,
      );

      step2 = models.PathStep(
        id: 'step-2',
        pathId: 'test-path',
        stepNumber: 2,
        title: 'Step 2',
        description: 'Second step',
        completionCriteria: 'Complete step 2',
        createdAt: now,
      );

      step3 = models.PathStep(
        id: 'step-3',
        pathId: 'test-path',
        stepNumber: 3,
        title: 'Step 3',
        description: 'Third step',
        completionCriteria: 'Complete step 3',
        createdAt: now,
      );
    });

    test('Admin user should access any step regardless of progress', () {
      // Test with no progress
      expect(_isStepLocked(step1, null, adminUser), false);
      expect(_isStepLocked(step2, null, adminUser), false);
      expect(_isStepLocked(step3, null, adminUser), false);

      // Test with some progress
      final progress = models.UserPathProgress(
        userId: 'admin-user',
        pathId: 'test-path',
        isCompleted: false,
        startedDate: DateTime.now(),
        lastAccessedDate: DateTime.now(),
      );

      expect(_isStepLocked(step1, progress, adminUser), false);
      expect(_isStepLocked(step2, progress, adminUser), false);
      expect(_isStepLocked(step3, progress, adminUser), false);
    });

    test('Regular user with no progress should only access first step', () {
      expect(_isStepLocked(step1, null, regularUser), false);
      expect(_isStepLocked(step2, null, regularUser), true);
      expect(_isStepLocked(step3, null, regularUser), true);
    });

    test('Regular user should access steps based on progress', () {
      // User on step 1
      final progressStep1 = models.UserPathProgress(
        userId: 'regular-user',
        pathId: 'test-path',
        isCompleted: false,
        startedDate: DateTime.now(),
        lastAccessedDate: DateTime.now(),
      );

      expect(
        _isStepLocked(step1, progressStep1, regularUser),
        false,
      ); // Current step
      expect(
        _isStepLocked(step2, progressStep1, regularUser),
        true,
      ); // Future step
      expect(
        _isStepLocked(step3, progressStep1, regularUser),
        true,
      ); // Future step

      // User on step 2, completed step 1
      final stepProgress = {
        'step1': models.PathStepProgress(stepId: 'step1', isCompleted: true),
      };

      final progressStep2 = models.UserPathProgress(
        userId: 'regular-user',
        pathId: 'test-path',
        isCompleted: false,
        startedDate: DateTime.now(),
        lastAccessedDate: DateTime.now(),
        stepProgress: stepProgress,
      );

      expect(
        _isStepLocked(step1, progressStep2, regularUser),
        false,
      ); // Completed step
      expect(
        _isStepLocked(step2, progressStep2, regularUser),
        false,
      ); // Current step
      expect(
        _isStepLocked(step3, progressStep2, regularUser),
        true,
      ); // Future step

      // User completed all steps
      final completedStepProgress = {
        'step1': models.PathStepProgress(stepId: 'step1', isCompleted: true),
        'step2': models.PathStepProgress(stepId: 'step2', isCompleted: true),
        'step3': models.PathStepProgress(stepId: 'step3', isCompleted: true),
      };

      final progressCompleted = models.UserPathProgress(
        userId: 'regular-user',
        pathId: 'test-path',
        isCompleted: true,
        startedDate: DateTime.now(),
        lastAccessedDate: DateTime.now(),
        completionDate: DateTime.now(),
        stepProgress: completedStepProgress,
      );

      expect(
        _isStepLocked(step1, progressCompleted, regularUser),
        false,
      ); // Completed step
      expect(
        _isStepLocked(step2, progressCompleted, regularUser),
        false,
      ); // Completed step
      expect(
        _isStepLocked(step3, progressCompleted, regularUser),
        false,
      ); // Completed step
    });
  });
}

/// Helper function that mimics the logic from GuidedPathDetailPage
bool _isStepLocked(
  models.PathStep step,
  models.UserPathProgress? userProgress,
  models.User? currentUser,
) {
  // Admin users can access any step
  if (currentUser?.isAdmin == true) {
    return false;
  }

  // If no progress exists, only the first step should be accessible
  if (userProgress == null) {
    return step.stepNumber != 1;
  }

  // If it's the first step, it's never locked
  if (step.stepNumber == 1) {
    return false;
  }

  // For simplicity in tests, check if step progress exists and is completed
  final stepProgress = userProgress.stepProgress;
  if (stepProgress == null) return true;

  // Check if previous step is completed (simplified logic)
  final previousStepKey = 'step${step.stepNumber - 1}';
  final previousStepProgress = stepProgress[previousStepKey];

  return previousStepProgress?.isCompleted != true;
}
