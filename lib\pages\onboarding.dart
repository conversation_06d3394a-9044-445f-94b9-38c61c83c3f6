import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/firestore.dart';
import '../services/analytics_service.dart';
import '../services/performance_service.dart';
import '../theme/theme.dart';
import '../widgets/persona_video_modal.dart';
import '../widgets/persona_selection_carousel.dart';
import 'main_navigation.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  List<SystemPersona> _systemPersonas = [];
  Set<String> _selectedPersonaIds = {};
  bool _isLoading = false;
  bool _isLoadingPersonas = true;

  @override
  void initState() {
    super.initState();
    _loadSystemPersonas();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadSystemPersonas() async {
    try {
      final personas = await FirestoreService.getActiveSystemPersonas();
      setState(() {
        _systemPersonas = personas;
        _isLoadingPersonas = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingPersonas = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to load coaches: $e')));
      }
    }
  }

  Future<void> _completeOnboarding() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedPersonaIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one coach')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Measure onboarding completion performance
      await PerformanceService.instance.measureOnboardingCompletion(() async {
        await FirestoreService.updateUserOnboarding(
          userId: currentUser.uid,
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          preferredPersonaIds: _selectedPersonaIds.toList(),
        );

        // Track onboarding completion in analytics
        await AnalyticsService.instance.logOnboardingComplete();

        // Update user properties to reflect onboarded status
        await AnalyticsService.instance.setUserProperty(
          name: 'is_onboarded',
          value: 'true',
        );

        // Track selected personas count
        await AnalyticsService.instance.setUserProperty(
          name: 'preferred_personas_count',
          value: _selectedPersonaIds.length.toString(),
        );

        // Log persona selections
        for (final personaId in _selectedPersonaIds) {
          await AnalyticsService.instance.logPersonaSelected(
            personaId: personaId,
            selectionContext: 'onboarding',
          );
        }
      }, _selectedPersonaIds.length);

      if (mounted) {
        // Navigate to main app
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainNavigationPage()),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to complete onboarding: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: AppDimensions.paddingL,
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section
                SizedBox(
                  height: AppDimensions.spacingXl + AppDimensions.spacingS,
                ),
                Text('Welcome!', style: AppTypography.onboardingTitle),
                SizedBox(height: AppDimensions.spacingS),
                Text(
                  'Let\'s get you set up. You can modify all of this information later in settings.',
                  style: AppTypography.onboardingSubtitle.copyWith(
                    color: context.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                SizedBox(
                  height: AppDimensions.spacingXl + AppDimensions.spacingS,
                ),

                // User Information Form
                Text(
                  'Tell us about yourself',
                  style: context.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingM),

                // Name Input
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'What should we call you?',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter your name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description Input
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Tell us about yourself',
                    hintText:
                        'Share details about your age, interests, work, family, hobbies, etc. This helps our AI coaches provide better guidance.',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 4,
                  textInputAction: TextInputAction.newline,
                ),
                const SizedBox(height: 16),

                // Upload Photo Button (UI only)
                OutlinedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Photo upload coming soon!'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Upload Photo'),
                ),
                const SizedBox(height: 40),

                // Coach Selection Section
                Text(
                  'Choose Your Preferred Coaches',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                Text(
                  'Select up to 3 coaches that resonate with you',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 16),

                // System Personas Carousel
                if (_isLoadingPersonas)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: CircularProgressIndicator(),
                    ),
                  )
                else if (_systemPersonas.isEmpty)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: Text('No coaches available'),
                    ),
                  )
                else
                  PersonaSelectionCarousel(
                    personas: _systemPersonas,
                    selectedPersonaIds: _selectedPersonaIds.toList(),
                    allowMultiSelection: true,
                    maxSelections: 3,
                    onMultiPersonaSelected: (selectedIds) {
                      setState(() {
                        _selectedPersonaIds = selectedIds.toSet();
                      });
                    },
                    onAvatarTap: _showPersonaVideo,
                    showSelectionIndicator: true,
                  ),

                const SizedBox(height: 40),

                // Complete Onboarding Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _completeOnboarding,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text(
                            'Complete Onboarding',
                            style: TextStyle(fontSize: 16),
                          ),
                  ),
                ),
                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showPersonaVideo(SystemPersona persona) {
    PersonaVideoModal.show(context, persona: persona);
  }
}
