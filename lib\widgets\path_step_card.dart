import 'package:flutter/material.dart';
import '../models/models.dart';
import '../theme/theme.dart';

class PathStepCard extends StatelessWidget {
  final PathStep pathStep;
  final UserPathProgress? userProgress;
  final bool isCurrentStep;
  final bool isCompleted;
  final bool isLocked;
  final VoidCallback? onTap;
  final VoidCallback? onComplete;

  const PathStepCard({
    super.key,
    required this.pathStep,
    this.userProgress,
    this.isCurrentStep = false,
    this.isCompleted = false,
    this.isLocked = false,
    this.onTap,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: AppDimensions.paddingHorizontalM.add(
        const EdgeInsets.symmetric(vertical: 6),
      ),
      elevation: isCurrentStep
          ? AppDimensions.elevationM
          : AppDimensions.elevationXs,
      child: InkWell(
        onTap: isLocked ? null : onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: isCurrentStep
                ? Border.all(color: colorScheme.primary, width: 2)
                : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Step number and status indicator
                _buildStepIndicator(context),

                const SizedBox(width: 16),

                // Step content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        pathStep.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: isLocked
                              ? colorScheme.onSurface.withValues(alpha: 0.5)
                              : colorScheme.onSurface,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Description
                      Text(
                        pathStep.description,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: isLocked
                              ? colorScheme.onSurface.withValues(alpha: 0.4)
                              : colorScheme.onSurface.withValues(alpha: 0.8),
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 12),

                      // Completion criteria
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: colorScheme.surfaceContainerHighest.withValues(
                            alpha: 0.5,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.task_alt,
                              size: 16,
                              color: colorScheme.primary,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                pathStep.completionCriteria,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: isLocked
                                      ? colorScheme.onSurface.withValues(
                                          alpha: 0.4,
                                        )
                                      : colorScheme.onSurface.withValues(
                                          alpha: 0.7,
                                        ),
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStepIndicator(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Widget icon;
    Color backgroundColor;
    Color foregroundColor;

    if (isLocked) {
      icon = Icon(Icons.lock, size: 16);
      backgroundColor = colorScheme.onSurface.withValues(alpha: 0.2);
      foregroundColor = colorScheme.onSurface.withValues(alpha: 0.5);
    } else if (isCompleted) {
      icon = Icon(AppIcons.completed, size: AppDimensions.iconS);
      backgroundColor = AppColors.success;
      foregroundColor = AppColors.onPrimary;
    } else if (isCurrentStep) {
      icon = Text(
        '${pathStep.stepNumber}',
        style: theme.textTheme.labelMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      );
      backgroundColor = colorScheme.primary;
      foregroundColor = colorScheme.onPrimary;
    } else {
      icon = Text('${pathStep.stepNumber}', style: theme.textTheme.labelMedium);
      backgroundColor = colorScheme.surfaceContainerHighest;
      foregroundColor = colorScheme.onSurface.withValues(alpha: 0.7);
    }

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(color: backgroundColor, shape: BoxShape.circle),
      child: Center(
        child: DefaultTextStyle(
          style: TextStyle(color: foregroundColor),
          child: icon,
        ),
      ),
    );
  }
}
